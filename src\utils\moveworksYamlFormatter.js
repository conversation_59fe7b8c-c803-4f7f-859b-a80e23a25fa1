import yaml from 'js-yaml'

/**
 * Converts JSON data to Moveworks Compound Action YAML format
 * Following the specifications from MW_CA_DOC.txt
 */

/**
 * Detects if JSON structure looks like it could be converted to a Moveworks Compound Action
 */
export function isMoveworksCompatible(jsonObject) {
  if (!jsonObject || typeof jsonObject !== 'object') return false
  
  // Check for common Moveworks patterns
  const hasSteps = Array.isArray(jsonObject.steps)
  const hasActions = Object.values(jsonObject).some(val => 
    val && typeof val === 'object' && (val.action_name || val.code)
  )
  const hasWorkflowStructure = jsonObject.workflow || jsonObject.actions || jsonObject.compound_action
  
  return hasSteps || hasActions || hasWorkflowStructure
}

/**
 * Converts a generic JSON object to Moveworks Compound Action format
 */
export function convertToMoveworksYaml(jsonObject, options = {}) {
  const {
    addComments = true,
    useStepsWrapper = true,
    addProgressUpdates = true
  } = options

  let moveworksStructure = {}
  
  // If already has steps structure, preserve it
  if (jsonObject.steps && Array.isArray(jsonObject.steps)) {
    moveworksStructure = jsonObject
  } else {
    // Convert generic structure to Moveworks format
    moveworksStructure = convertGenericToMoveworks(jsonObject, { addProgressUpdates })
  }

  // Apply Moveworks-specific formatting
  const yamlOptions = {
    indent: 2,
    lineWidth: -1,
    noRefs: true,
    sortKeys: false,
    quotingType: '"',
    forceQuotes: false
  }

  let yamlString = yaml.dump(moveworksStructure, yamlOptions)
  
  if (addComments) {
    yamlString = addMoveworksComments(yamlString, moveworksStructure)
  }

  return yamlString
}

/**
 * Converts generic JSON structure to Moveworks Compound Action format
 */
function convertGenericToMoveworks(jsonObject, options = {}) {
  const { addProgressUpdates } = options
  
  // If it's a simple object, create a basic compound action
  if (!Array.isArray(jsonObject) && typeof jsonObject === 'object') {
    const steps = []
    
    // Look for action-like structures
    Object.entries(jsonObject).forEach(([key, value], index) => {
      if (value && typeof value === 'object') {
        const step = convertObjectToAction(key, value, index, addProgressUpdates)
        if (step) steps.push(step)
      }
    })
    
    // If no steps were created, create a basic script action
    if (steps.length === 0) {
      steps.push(createBasicScriptAction(jsonObject))
    }
    
    return { steps }
  }
  
  // If it's an array, treat each item as a step
  if (Array.isArray(jsonObject)) {
    const steps = jsonObject.map((item, index) => {
      if (typeof item === 'object' && item !== null) {
        return convertObjectToAction(`step_${index + 1}`, item, index, addProgressUpdates)
      }
      return createBasicScriptAction({ value: item }, `step_${index + 1}`)
    }).filter(Boolean)
    
    return { steps }
  }
  
  // For primitive values, create a simple return action
  return {
    steps: [{
      return: {
        output_mapper: {
          result: typeof jsonObject === 'string' ? `"${jsonObject}"` : jsonObject
        }
      }
    }]
  }
}

/**
 * Converts an object to a Moveworks action step
 */
function convertObjectToAction(key, obj, index, addProgressUpdates = true) {
  // Check if it already looks like a Moveworks action
  if (obj.action_name || obj.code) {
    return { action: obj }
  }
  
  // Check for HTTP-like structure
  if (obj.url || obj.endpoint || obj.method) {
    return createHttpAction(key, obj, addProgressUpdates)
  }
  
  // Check for user-related data (might use mw.get_user_by_email)
  if (obj.email || obj.user_email || obj.user_id) {
    return createUserAction(key, obj, addProgressUpdates)
  }
  
  // Check for notification-like structure
  if (obj.message || obj.notification || obj.alert) {
    return createNotificationAction(key, obj, addProgressUpdates)
  }
  
  // Default: create a script action to process the data
  return createScriptAction(key, obj, index)
}

/**
 * Creates an HTTP action step
 */
function createHttpAction(key, obj, addProgressUpdates) {
  const action = {
    action_name: obj.action_name || `${key}_http_call`,
    output_key: `${key}_result`,
    input_args: {}
  }
  
  // Map common HTTP fields
  if (obj.url) action.input_args.url = `data.${key}_url`
  if (obj.method) action.input_args.method = obj.method
  if (obj.headers) action.input_args.headers = obj.headers
  if (obj.body || obj.data) action.input_args.body = obj.body || obj.data
  
  if (addProgressUpdates) {
    action.progress_updates = {
      on_pending: `"Making HTTP request to ${obj.url || '{{data.url}}'}..."`,
      on_complete: `"HTTP request completed successfully."`
    }
  }
  
  return { action }
}

/**
 * Creates a user lookup action using Moveworks built-in
 */
function createUserAction(key, obj, addProgressUpdates) {
  const action = {
    action_name: 'mw.get_user_by_email',
    output_key: `${key}_user_details`,
    input_args: {
      user_email: obj.email || obj.user_email || `data.${key}_email`
    }
  }
  
  if (addProgressUpdates) {
    action.progress_updates = {
      on_pending: `"Looking up user details..."`,
      on_complete: `"User details retrieved successfully."`
    }
  }
  
  return { action }
}

/**
 * Creates a notification action using Moveworks built-in
 */
function createNotificationAction(key, obj, addProgressUpdates) {
  const action = {
    action_name: 'mw.send_plaintext_chat_notification',
    output_key: `${key}_notification_status`,
    input_args: {
      user_record_id: obj.user_id || obj.recipient_id || `data.user_details.user.id`,
      message: obj.message || obj.notification || obj.alert
    }
  }
  
  if (addProgressUpdates) {
    action.progress_updates = {
      on_pending: `"Sending notification..."`,
      on_complete: `"Notification sent successfully."`
    }
  }
  
  return { action }
}

/**
 * Creates a script action for data processing
 */
function createScriptAction(key, obj, index) {
  const inputArgs = {}
  
  // Convert object properties to input arguments
  Object.entries(obj).forEach(([prop, value]) => {
    if (typeof value !== 'object') {
      inputArgs[prop] = `data.${prop}`
    }
  })
  
  const code = generateAPIthonCode(obj, key)
  
  return {
    script: {
      output_key: `${key}_processed`,
      input_args: inputArgs,
      code: code
    }
  }
}

/**
 * Creates a basic script action for simple data
 */
function createBasicScriptAction(obj, key = 'data_processing') {
  return {
    script: {
      output_key: `${key}_result`,
      input_args: {
        input_data: 'data.input_data'
      },
      code: `# Process the input data\nresult = input_data\nresult`
    }
  }
}

/**
 * Generates APIthon code for processing an object
 */
function generateAPIthonCode(obj, key) {
  const properties = Object.keys(obj)
  
  if (properties.length === 1) {
    const prop = properties[0]
    return `# Process ${key}\nresult = ${prop}\nresult`
  }
  
  return `# Process ${key} data
result = {
${properties.map(prop => `  "${prop}": ${prop}`).join(',\n')}
}
result`
}

/**
 * Adds helpful comments to the YAML for Moveworks context
 */
function addMoveworksComments(yamlString, structure) {
  let commented = `# Moveworks Compound Action
# Generated from JSON input - review and customize as needed
# 
# Key concepts:
# - Use 'data.' prefix to access input variables and previous step outputs
# - Built-in actions use 'mw.' prefix (e.g., mw.get_user_by_email)
# - Each action needs: action_name, output_key, input_args
# - Scripts use APIthon (Python-like) syntax
#
# For more details, see: https://help.moveworks.com/docs/compound-actions

`
  
  return commented + yamlString
}

/**
 * Validates if YAML follows Moveworks Compound Action format
 */
export function validateMoveworksYaml(yamlString) {
  try {
    const parsed = yaml.load(yamlString)
    const issues = []
    
    // Check for steps structure
    if (!parsed.steps && !parsed.action && !parsed.script && !parsed.return) {
      issues.push("Missing 'steps' array or single action/script/return")
    }
    
    // Validate steps if present
    if (parsed.steps && Array.isArray(parsed.steps)) {
      parsed.steps.forEach((step, index) => {
        validateStep(step, index, issues)
      })
    }
    
    return {
      isValid: issues.length === 0,
      issues: issues
    }
  } catch (error) {
    return {
      isValid: false,
      issues: [`Invalid YAML syntax: ${error.message}`]
    }
  }
}

/**
 * Validates a single step in the compound action
 */
function validateStep(step, index, issues) {
  const stepTypes = ['action', 'script', 'switch', 'for', 'parallel', 'try_catch', 'return', 'raise']
  const hasValidType = stepTypes.some(type => step[type])
  
  if (!hasValidType) {
    issues.push(`Step ${index + 1}: Must contain one of: ${stepTypes.join(', ')}`)
  }
  
  // Validate action steps
  if (step.action) {
    if (!step.action.action_name) {
      issues.push(`Step ${index + 1}: Action missing 'action_name'`)
    }
    if (!step.action.output_key) {
      issues.push(`Step ${index + 1}: Action missing 'output_key'`)
    }
  }
  
  // Validate script steps
  if (step.script) {
    if (!step.script.code) {
      issues.push(`Step ${index + 1}: Script missing 'code'`)
    }
    if (!step.script.output_key) {
      issues.push(`Step ${index + 1}: Script missing 'output_key'`)
    }
  }
}

/**
 * Gets suggestions for improving Moveworks YAML
 */
export function getMoveworksSuggestions(yamlString) {
  const suggestions = []
  
  try {
    const parsed = yaml.load(yamlString)
    
    // Check for data access patterns
    const yamlText = yamlString.toLowerCase()
    if (!yamlText.includes('data.')) {
      suggestions.push({
        type: 'improvement',
        message: "Consider using 'data.' prefix to access input variables and previous step outputs"
      })
    }
    
    // Check for built-in actions
    if (yamlText.includes('get_user') && !yamlText.includes('mw.get_user')) {
      suggestions.push({
        type: 'improvement',
        message: "Use 'mw.get_user_by_email' for user lookups instead of custom actions"
      })
    }
    
    // Check for progress updates
    if (yamlText.includes('action_name') && !yamlText.includes('progress_updates')) {
      suggestions.push({
        type: 'enhancement',
        message: "Consider adding 'progress_updates' to actions for better user experience"
      })
    }
    
    // Check for error handling
    if (parsed.steps && parsed.steps.length > 1 && !yamlText.includes('try_catch')) {
      suggestions.push({
        type: 'enhancement',
        message: "Consider adding error handling with 'try_catch' for robust workflows"
      })
    }
    
  } catch (error) {
    suggestions.push({
      type: 'error',
      message: `Fix YAML syntax errors first: ${error.message}`
    })
  }
  
  return suggestions
}
